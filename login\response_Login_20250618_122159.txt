================================================================================
gRPC调用结果 - 2025-06-18 12:21:59
配置文件: login/login_with_metadata.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "lvmeng", "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced

📥 响应信息:
   📊 响应Body:
      Resolved method descriptor:
      rpc Login ( .bimbase.api.LoginRequest ) returns ( .bimbase.api.LoginResponse ) {
        option (.google.api.http) = {
          get: "/v1/Login/username/{userName}/passwordMd5/{passwordMd5}"
        };
      }
      
      Request metadata to send:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced
      
      Response headers received:
      cache-control: no-cache,no-store
      content-type: application/grpc
      date: Wed, 18 Jun 2025 04:21:59 GMT
      expires: -1
      pragma: no-cache
      server: Kestrel
      set-cookie: .AspNetCore.Session=CfDJ8JmiJM0C43ZFuEQB2tyVbE9O79icFG3yPv8KQCl%2BXpnNKzWIhC58OKiG4a2J%2F6yWTSHKNwXWqfOSXtdh667AzDYi37MpfQI01usDfNENRBTh%2B9ovpQB1LpA2xKrqqIThcj%2BKHi%2BTn%2FZAhUIhbxd%2FzlJ80r7lAYOd3LJvTWZclhcr; path=/; samesite=lax; httponly
      
      Response contents:
      {
        "isSuccess": true,
        "message": "登录成功。",
        "sessionId": "fcced42fba9847d581d2d1e0de95fbe7"
      }
      
      Response trailers received:
      processing-time-ms: 19
      request-id: 0HNDDJIGOV2JJ:00000001
      Sent 1 request and received 1 response

🔧 调用详情:
   返回码: 0

================================================================================
