syntax = "proto3";

option csharp_namespace = "BimBase.Api.Protos";
import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "bimbaseshared.proto";


package bimbase.api;

message DownLoadPublishProvideDataByVerisonNameRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	string mainVersionName = 3;
	string subVersionName = 4;
}
message DownLoadPublishProvideDataByVerisonNameResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcBPExObjectPublish bpExObjectPublishs = 4;
}
message GetAllProvideVerionListRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
}
message GetAllProvideVerionListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcProvideVersionInfo pvInfoList = 4;
}
message GetAllPublishProvideVerionInfoListRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	bool isShowSubVersion = 3;
}
message GetAllPublishProvideVerionInfoListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcProvideVersionInfo pvInfoList = 4;
}
message GetPublishProvideVersionInfoListByGuidRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	string guid = 3;
}
message GetPublishProvideVersionInfoListByGuidResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcProvideVersionInfo pvInfoList = 4;
}
message GetNewestPublishProvideWithVersionInfoListByGuidRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	string guid = 3;
}
message BPExObjectWithVersionInfo{
	int64 Id = 1;
	string ProvideUser = 2;
	string ProvideTime = 3;
	int32 ProvideState = 4;
	string ProvideNotes = 5;
	string AcceptUser = 6;
	string AcceptTime = 7;
	int32 AcceptState = 8;
	string StructNotes = 9;
	int32 Domain = 10;
	string LoadName = 11;
	string GUID = 12;
	string ExtendField = 13;
	int32 Type = 14;
	string VersionName = 15;
	string SubVersionName = 16;
}
message GetNewestPublishProvideWithVersionInfoListByGuidResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	BPExObjectWithVersionInfo pbwithpvInfo = 4;
}

message DownLoadAllProvideDataRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
}
message DownLoadAllProvideDataResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcBPExObject bpExObjectList = 4;
}

message GetAllMainProjectMemberNameListRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
}
message GetAllMainProjectMemberNameListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated string memberNameList = 4;
}
message GetMPUserGroupByMemberIdRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	string memberId = 3;
}
message GetMPUserGroupByMemberIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPUserGroup userGroups = 4;
}
message GetMPUserGroupByIdRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	int32 mpUserGroupId = 3;
}
message GetMPUserGroupByIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcMPUserGroup mPUserGroup = 4;
}
message GetMPUserGroupsRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
}
message GetMPUserGroupsResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPUserGroup userGroups = 4;
}
message GetAllUserGroupMembersRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
}
message GetAllUserGroupMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPUserGroupMember mPUserGroupMembers = 4;
}
message GetMPUserGroupMembersRequest{
	string sessionId = 1;
	string mainProjectGuid = 2;
	int32 mPUserGroupId = 3;
}
message GetMPUserGroupMembersResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcTeamMember memberTmp = 4;
}
message GetMPTeamProjectByIdRequest{
	string sessionId = 1;
	string mpTeamProjectGuid = 2;
	string mainprojectGuid = 3;
}
message GetMPTeamProjectByIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcMPTeamProject mptp = 4;
}
message GetMPTeamProjectListRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetMPTeamProjectListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPTeamProject mptpList = 4;
}
message GetMPProjectTreeNodesByProjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string mpTeamProjectGuid = 3;
}
message GetMPProjectTreeNodesByProjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetMPProjectTreeNodeByNodeIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	int64 instanceid = 3;
}
message GetMPProjectTreeNodeByNodeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcMPProjectTreeNode mpNode = 4;
}
message GetMPProjectTreeNodesByTreeIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int64 treeId = 4;
}
message GetMPProjectTreeNodesByTreeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetMPProjectTreeNodesRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetMPProjectTreeNodesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetLibDataIdAndFileMD5ByLibIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string LibGuid = 3;
}
message GetLibDataIdAndFileMD5ByLibIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPLibraryData mPLibraryInfos = 4;
}
message GetMPUserGroupInfoAndAuthInfoByTreeNodeIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	repeated int64 treeNodeIdList = 4;
}
message GroupInfoWithAuthInfo{
	GrpcMPUserGroup mPUserGroupInfo = 1;
	GrpcMPUserGroupAuth mPUserGroupAuthInfo = 2;
	repeated GrpcMPAuthInfo mPAuthInfos = 3;
}
message TreeIdWithGroupInfoWithAuthInfo{
	int64 treeId = 1;
	repeated GroupInfoWithAuthInfo dicGroupInfoWithAuthInfos = 2;
}
message GetMPUserGroupInfoAndAuthInfoByTreeNodeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated TreeIdWithGroupInfoWithAuthInfo dicGroupInfoWithAuthInfos = 4;
}
message GetUserNodeAuthsBySubProjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
}
message GroupAuthInfo{
	GrpcMPUserGroupAuth mPUserGroupAuth = 1;
	repeated GrpcMPAuthInfo mPAuthInfos = 2;
}
message GetUserNodeAuthsBySubProjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GroupAuthInfo groupAuthInfos = 4;
}
message GetMPUerGroupAuthByObjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string objectGuid = 3;
	int64 nodeid = 4;
	int32 groupid = 5;
	int32 objecttype = 6;
}
message GetMPUerGroupAuthByObjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPAuthInfo authInfos = 4;
}
message GetCurrentUserMPAuthInfosByObjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string objectGuid = 3;
	int64 nodeid = 4;
	int32 objecttype = 5;
}
message GetCurrentUserMPAuthInfosByObjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPAuthInfo authInfos = 4;
}
message GetUserProjectNodeAuthListRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
}
message GetUserProjectNodeAuthListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated int64 instanceidList = 4;
}
message GroupWithAuth{
	int32 groupId = 1;
	repeated GrpcMPAuthInfo auths = 2;
}
message GetAllUserMPAuthInfosByObjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string objectGuid = 3;
	int64 nodeid = 4;
	int32 objecttype = 5;
}
message GetAllUserMPAuthInfosByObjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GroupWithAuth authInfos = 4;
}
message GetMPProjectTreeNodeInfoBySubProjectIdRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
}
message GetMPProjectTreeNodeInfoBySubProjectIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode treenodeInfo = 4;
}
message GetMPProjectTreeNodeInfoRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int64 treeId = 4;
}
message GetMPProjectTreeNodeInfoResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode treenodeInfo = 4;
}
message GetLibraryInfoByMainProjectGuidReqeust{
	string sessionId = 1;
	string mainprojectGuid = 2;
	GrpcLibType libType = 3;
}
message GetLibraryInfoByMainProjectGuidResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	GrpcMPLibraryInfo mplib = 4;
}
message GetAllLibAuthsRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetAllLibAuthsResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPUserGroupLibAuth libAuthlist = 4;
}
message GetAllMPLinkFileHistoryBySubProjectGuidRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subprojectGuid = 3;
}
message GetAllMPLinkFileHistoryBySubProjectGuidResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPCloudLinkFile mPCloudLinkFiles = 4;
}
message DownloadMPLinkFileDataBySubProjectGuidAndVersionRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subprojectGuid = 3;
	int32 versionNo = 4;
}
message DownloadMPLinkFileDataBySubProjectGuidAndVersionResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string filename = 4;
	bytes outFileData = 5;
}
message GetAllMPLinkFileHistoryRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetAllMPLinkFileHistoryResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPCloudLinkFile mPCloudLinkFiles = 4;
}
message GetAllMPLinkFileNewestVersionRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetAllMPLinkFileNewestVersionResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPCloudLinkFile mPCloudLinkFiles = 4;
}
message GetMPReleaseConfigBySourceAndTargetGuidRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string targetSubProjectGuid = 3;
}
message GetMPReleaseConfigBySourceAndTargetGuidResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPReleaseConfig mPReleaseConfigs = 4;
}
message GetAllMPReleaseConfigsRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message GetAllMPReleaseConfigsResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPReleaseConfig mPReleaseConfigs = 4;
}
message InitMPMsgUserListRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message MPMsgUserInfo{
	string MemId = 1;
	string DisplayNmae = 2;
	bytes AvatarData = 3;
}
message InitMPMsgUserListResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated MPMsgUserInfo userList = 4;
}
message GetAllUnSendMPMessagesRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
}
message GetAllUnSendMPMessagesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPMessage unSendMessages = 4;
}
message GetGroupHistoryMPMessageRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int32 groupId = 4;
}
message GetGroupHistoryMPMessageResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPMessage groupMessages = 4;
}
message GetHistoryMPMessagesRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	string talkUserId = 4;
	int32 groupid = 5;
}
message GetHistoryMPMessagesResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPMessage groupMessages = 4;
}
message InitMPGroupListByLogUserRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
}
message MPMsgUserInfoWithGroupId{
	int32 groupId = 1;
	repeated MPMsgUserInfo userList = 2;
}
message InitMPGroupListByLogUserResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPUserGroup logUserGroups = 4;
	repeated MPMsgUserInfoWithGroupId groupUserList = 5;
}
message CheckDrawingLockStatuRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	repeated int64 instanceIdList = 4;
}
message DownLoadMPDrawingRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int64 instanceId = 4;
}
message DownLoadMPDrawingResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string filename = 4;
	bytes outFileData = 5;
}
message GetMPDrawingFileMD5Request{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int64 instanceId = 4;
}
message GetMPDrawingFileMD5Response{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string fileMD5 = 4;
}
message DownLoadMPProjectTreeNodeDataRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
	int32 versionNo = 4;
	int64 treeId = 5;
}
message DownLoadMPProjectTreeNodeDataResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode treeNodeInfoList = 4;
	repeated GrpcHistoryData treeNodeChangeDataList = 5;
	int32 versionNo = 6;
}
message DownLoadLibTree{
	string MainProjectGuid = 1;
	string LibGuid = 2;
	int64 TreeId = 3;
	int32 ClientVersion = 4;
}
message DownLoadLibraryTreeNodeRequest{
	string sessionId = 1;
	repeated DownLoadLibTree libGuidAndTreeId = 2;
}
message DownLoadLibraryTreeNodeResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPCatalogTreeNode treeNodeInfoList = 4;
	repeated GrpcMPLibraryDataHistory libDataHistoryList = 5;
	map<string,int32> dicLibVersion = 6;
}

message DownLoadLibraryTreeNodeLargeRequest{
	string sessionId = 1;
	repeated DownLoadLibTree libGuidAndTreeId = 2;
	int64 requestId = 3;
	repeated int32 toDownMPCataLogTreeNodeIdList = 4;
	repeated int64 toDownMPLibDataHistoryIdList = 5;
	repeated GrpcOperationRecordType toDownLibDataHistoryOpertypeList = 6;
	
}
message DownLoadLibraryTreeNodeLargeResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPCatalogTreeNode mpCataLogTreeNodeList = 4;
	repeated GrpcMPLibraryDataHistory mpLibDataHistoryList = 5;
	map<string,int32> dicLibVersion = 6;
	repeated int32 toDownMPCataLogTreeNodeIdList = 7;
	repeated int64 toDownMPLibDataHistoryIdList = 8;
	repeated GrpcOperationRecordType toDownLibDataHistoryOpertypeList = 9;
	int64 requestId = 10;
}
message DownLoadLibFileDataRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string libGuid = 3;
	int64 dataId = 4;
}
message DownLoadLibFileDataResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string filename = 4;
	bytes outFileData = 5;
}
message GetLibraryFileMD5Request{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string libGuid = 3;
	int64 dataId = 4;
}

message GetLibraryFileMD5Response{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	string fileMD5 = 4;
}
message GetMPTeamProjectInitStateRequest{
	string sessionId = 1;
	string mainprojectGuid = 2;
	string subProjectGuid = 3;
}
message GetMPTeamProjectInitStateResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	int32 initState = 4;
}
message MPLibraryDataHistoryForClient
{
	int32 ID = 1;
    string LibId = 2;
    int64 DataId = 3;
    GrpcOperationRecordType OperationType = 4;
    int32 VersionNo = 5;
    string SchemaName = 6;
    string ClassName = 7;
}
message DownLoadLibraryTreeNodeChangeRequest{
	string sessionId = 1;
	repeated DownLoadLibTree libGuidAndTreeId = 2;
}
message DownLoadLibraryTreeNodeChangeResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated MPLibraryDataHistoryForClient libDataHistoryList = 4;
	map<string,int32> dicLibVersion = 5;
}
//enum GrpcTreeNodeSearch {
//  Accurate = 0; // 精确
 // PreCheck = 1;//左查
//  LaterCheck = 2;//右查
 // Involve = 3;//包含
//}
message GetMPProjectTreeNodesByNodeNameRequest{
	string sessionId = 1;
	string nodeName = 2;
	string mainProjectGuid=3;
	string subProjectGuid=4;
	int32 treeNodeSearch=5;
	int32 offset = 6;
	int32 count = 7;
}
message GetMPProjectTreeNodesByNodeNameResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetParentsMPProjectTreeNodesByNodeIdRequest{
	string sessionId = 1;
	int64 nodeId = 2;
	string mainProjectGuid=3;
	string subProjectGuid=4;
}
message GetParentsMPProjectTreeNodesByNodeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetChildsMPProjectTreeNodesByNodeIdRequest{
	string sessionId = 1;
	int64 nodeId = 2;
	int32 nextLevel = 3;
	string mainProjectGuid=4;
	string subProjectGuid=5;
}
message GetChildsMPProjectTreeNodesByNodeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	repeated GrpcMPProjectTreeNode mpNodes = 4;
}
message GetChildsMPProjectTreeCountByNodeIdRequest{
	string sessionId = 1;
	int64 nodeId = 2;
	int32 nextLevel = 3;
	string mainProjectGuid=4;
	string subProjectGuid=5;
}
message GetChildsMPProjectTreeCountByNodeIdResponse{
	reserved 1;
	bool isSuccess = 2;
	string message = 3;
	int32 treeCount = 4;
}

// 新增：服务端流式方法
message GetChildsMPProjectTreeNodesByNodeIdStreamRequest {
    string session_id = 1;
    string main_project_guid = 2;
    string sub_project_guid = 3;
    int64 node_id = 4;
    int32 next_level = 5;
    int32 batch_size = 6;                             // 每批数据大小，默认10
}

// 流式响应消息 - 每批数据的响应
message GetChildsMPProjectTreeNodesByNodeIdStreamResponse {
    bool is_success = 1;
    string message = 2;
    repeated GrpcMPProjectTreeNode mp_nodes = 3;      // 当前批次的节点数据
    int32 batch_index = 4;                            // 当前批次索引
    int32 batch_size = 5;                             // 当前批次大小
    int32 total_count = 6;                            // 总记录数（仅在第一批返回）
    bool is_final_batch = 7;                          // 是否为最后一批
}

service GrpcMainprojectQuery {
	rpc DownLoadPublishProvideDataByVerisonName(DownLoadPublishProvideDataByVerisonNameRequest) returns (DownLoadPublishProvideDataByVerisonNameResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadPublishProvideDataByVerisonName"
			body: "*"
		};
	}
	rpc GetAllProvideVerionList(GetAllProvideVerionListRequest) returns (GetAllProvideVerionListResponse){
		option (google.api.http) = {
			post: "/v1/GetAllProvideVerionList"
			body: "*"
		};
	}

	rpc GetAllPublishProvideVerionInfoList(GetAllPublishProvideVerionInfoListRequest) returns (GetAllPublishProvideVerionInfoListResponse){
		option (google.api.http) = {
			post: "/v1/GetAllPublishProvideVerionInfoList"
			body: "*"
		};
	}

	rpc GetPublishProvideVersionInfoListByGuid(GetPublishProvideVersionInfoListByGuidRequest) returns (GetPublishProvideVersionInfoListByGuidResponse){
		option (google.api.http) = {
			post: "/v1/GetPublishProvideVersionInfoListByGuid"
			body: "*"
		};
	}

	rpc GetNewestPublishProvideWithVersionInfoListByGuid(GetNewestPublishProvideWithVersionInfoListByGuidRequest) returns (GetNewestPublishProvideWithVersionInfoListByGuidResponse){
		option (google.api.http) = {
			post: "/v1/GetNewestPublishProvideWithVersionInfoListByGuid"
			body: "*"
		};
	}

	rpc DownLoadAllProvideData(DownLoadAllProvideDataRequest) returns (DownLoadAllProvideDataResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadAllProvideData"
			body: "*"
		};
	}

	rpc GetAllMainProjectMemberNameList(GetAllMainProjectMemberNameListRequest) returns (GetAllMainProjectMemberNameListResponse){
		option (google.api.http) = {
			post: "/v1/GetAllMainProjectMemberNameList"
			body: "*"
		};
	}

	rpc GetMPUserGroupByMemberId(GetMPUserGroupByMemberIdRequest) returns (GetMPUserGroupByMemberIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUserGroupByMemberId"
			body: "*"
		};
	}

	rpc GetMPUserGroupById(GetMPUserGroupByIdRequest) returns (GetMPUserGroupByIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUserGroupById"
			body: "*"
		};
	}

	rpc GetMPUserGroups(GetMPUserGroupsRequest) returns (GetMPUserGroupsResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUserGroups"
			body: "*"
		};
	}
	rpc GetAllUserGroupMembers(GetAllUserGroupMembersRequest) returns (GetAllUserGroupMembersResponse){
		option (google.api.http) = {
			post: "/v1/GetAllUserGroupMembers"
			body: "*"
		};
	}
	rpc GetMPUserGroupMembers(GetMPUserGroupMembersRequest) returns (GetMPUserGroupMembersResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUserGroupMembers"
			body: "*"
		};
	}
	//1203
	rpc GetMPTeamProjectById(GetMPTeamProjectByIdRequest) returns (GetMPTeamProjectByIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPTeamProjectById"
			body: "*"
		};
	}

	rpc GetMPTeamProjectList(GetMPTeamProjectListRequest) returns (GetMPTeamProjectListResponse){
		option (google.api.http) = {
			post: "/v1/GetMPTeamProjectList"
			body: "*"
		};
	}

	rpc GetMPProjectTreeNodesByProjectId(GetMPProjectTreeNodesByProjectIdRequest) returns (GetMPProjectTreeNodesByProjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodesByProjectId"
			body: "*"
		};
	}

	rpc GetMPProjectTreeNodeByNodeId(GetMPProjectTreeNodeByNodeIdRequest) returns (GetMPProjectTreeNodeByNodeIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodeByNodeId"
			body: "*"
		};
	}

	rpc GetMPProjectTreeNodesByTreeId(GetMPProjectTreeNodesByTreeIdRequest) returns (GetMPProjectTreeNodesByTreeIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodesByTreeId"
			body: "*"
		};
	}
	rpc GetMPProjectTreeNodes(GetMPProjectTreeNodesRequest) returns (GetMPProjectTreeNodesResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodes"
			body: "*"
		};
	}
	rpc GetLibDataIdAndFileMD5ByLibId(GetLibDataIdAndFileMD5ByLibIdRequest) returns (GetLibDataIdAndFileMD5ByLibIdResponse){
		option (google.api.http) = {
			post: "/v1/GetLibDataIdAndFileMD5ByLibId"
			body: "*"
		};
	}

	rpc GetMPUserGroupInfoAndAuthInfoByTreeNodeId(GetMPUserGroupInfoAndAuthInfoByTreeNodeIdRequest) returns (GetMPUserGroupInfoAndAuthInfoByTreeNodeIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUserGroupInfoAndAuthInfoByTreeNodeId"
			body: "*"
		};
	}

	rpc GetUserNodeAuthsBySubProjectId(GetUserNodeAuthsBySubProjectIdRequest) returns (GetUserNodeAuthsBySubProjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetUserNodeAuthsBySubProjectId"
			body: "*"
		};
	}
	rpc GetMPUerGroupAuthByObjectId(GetMPUerGroupAuthByObjectIdRequest) returns (GetMPUerGroupAuthByObjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPUerGroupAuthByObjectId"
			body: "*"
		};
	}

	rpc GetCurrentUserMPAuthInfosByObjectId(GetCurrentUserMPAuthInfosByObjectIdRequest) returns (GetCurrentUserMPAuthInfosByObjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetCurrentUserMPAuthInfosByObjectId"
			body: "*"
		};
	}

	rpc GetUserProjectNodeAuthList(GetUserProjectNodeAuthListRequest) returns (GetUserProjectNodeAuthListResponse){
		option (google.api.http) = {
			post: "/v1/GetUserProjectNodeAuthList"
			body: "*"
		};
	}
	rpc GetAllUserMPAuthInfosByObjectId(GetAllUserMPAuthInfosByObjectIdRequest) returns (GetAllUserMPAuthInfosByObjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetAllUserMPAuthInfosByObjectId"
			body: "*"
		};
	}
	rpc GetMPProjectTreeNodeInfoBySubProjectId(GetMPProjectTreeNodeInfoBySubProjectIdRequest) returns (GetMPProjectTreeNodeInfoBySubProjectIdResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodeInfoBySubProjectId"
			body: "*"
		};
	}

	rpc GetMPProjectTreeNodeInfo(GetMPProjectTreeNodeInfoRequest) returns (GetMPProjectTreeNodeInfoResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodeInfo"
			body: "*"
		};
	}

	rpc GetLibraryInfoByMainProjectGuid(GetLibraryInfoByMainProjectGuidReqeust) returns (GetLibraryInfoByMainProjectGuidResponse){
		option (google.api.http) = {
			post: "/v1/GetLibraryInfoByMainProjectGuid"
			body: "*"
		};
	}
	rpc GetAllLibAuths(GetAllLibAuthsRequest) returns (GetAllLibAuthsResponse){
		option (google.api.http) = {
			post: "/v1/GetAllLibAuths"
			body: "*"
		};
	}
	rpc GetAllMPLinkFileHistoryBySubProjectGuid(GetAllMPLinkFileHistoryBySubProjectGuidRequest) returns (GetAllMPLinkFileHistoryBySubProjectGuidResponse){
		option (google.api.http) = {
			post: "/v1/GetAllMPLinkFileHistoryBySubProjectGuid"
			body: "*"
		};
	}
	rpc DownloadMPLinkFileDataBySubProjectGuidAndVersion(DownloadMPLinkFileDataBySubProjectGuidAndVersionRequest) returns (DownloadMPLinkFileDataBySubProjectGuidAndVersionResponse){
		option (google.api.http) = {
			post: "/v1/DownloadMPLinkFileDataBySubProjectGuidAndVersion"
			body: "*"
		};
	}

	rpc GetAllMPLinkFileHistory(GetAllMPLinkFileHistoryRequest) returns (GetAllMPLinkFileHistoryResponse){
		option (google.api.http) = {
			post: "/v1/GetAllMPLinkFileHistory"
			body: "*"
		};
	}
	rpc GetAllMPLinkFileNewestVersion(GetAllMPLinkFileNewestVersionRequest) returns (GetAllMPLinkFileNewestVersionResponse){
		option (google.api.http) = {
			post: "/v1/GetAllMPLinkFileNewestVersion"
			body: "*"
		};
	}
	rpc GetMPReleaseConfigBySourceAndTargetGuid(GetMPReleaseConfigBySourceAndTargetGuidRequest) returns (GetMPReleaseConfigBySourceAndTargetGuidResponse){
		option (google.api.http) = {
			post: "/v1/GetMPReleaseConfigBySourceAndTargetGuid"
			body: "*"
		};
	}

	rpc GetAllMPReleaseConfigs(GetAllMPReleaseConfigsRequest) returns (GetAllMPReleaseConfigsResponse){
		option (google.api.http) = {
			post: "/v1/GetAllMPReleaseConfigs"
			body: "*"
		};
	}
	rpc InitMPMsgUserList(InitMPMsgUserListRequest) returns (InitMPMsgUserListResponse){
		option (google.api.http) = {
			post: "/v1/InitMPMsgUserList"
			body: "*"
		};
	}
	rpc GetAllUnSendMPMessages(GetAllUnSendMPMessagesRequest) returns (GetAllUnSendMPMessagesResponse){
		option (google.api.http) = {
			post: "/v1/GetAllUnSendMPMessages"
			body: "*"
		};
	}
	rpc GetGroupHistoryMPMessage(GetGroupHistoryMPMessageRequest) returns (GetGroupHistoryMPMessageResponse){
		option (google.api.http) = {
			post: "/v1/GetGroupHistoryMPMessage"
			body: "*"
		};
	}

	rpc GetHistoryMPMessages(GetHistoryMPMessagesRequest) returns (GetHistoryMPMessagesResponse){
		option (google.api.http) = {
			post: "/v1/GetHistoryMPMessages"
			body: "*"
		};
	}
	rpc InitMPGroupListByLogUser(InitMPGroupListByLogUserRequest) returns (InitMPGroupListByLogUserResponse){
		option (google.api.http) = {
			post: "/v1/InitMPGroupListByLogUser"
			body: "*"
		};
	}
	rpc CheckDrawingLockStatu(CheckDrawingLockStatuRequest) returns (GrpcResult){
		option (google.api.http) = {
			post: "/v1/CheckDrawingLockStatu"
			body: "*"
		};
	}

	rpc DownLoadMPDrawing(DownLoadMPDrawingRequest) returns (DownLoadMPDrawingResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadMPDrawing"
			body: "*"
		};
	}
	rpc GetMPDrawingFileMD5(GetMPDrawingFileMD5Request) returns (GetMPDrawingFileMD5Response){
		option (google.api.http) = {
			post: "/v1/GetMPDrawingFileMD5"
			body: "*"
		};
	}

	rpc DownLoadMPProjectTreeNodeData(DownLoadMPProjectTreeNodeDataRequest) returns (DownLoadMPProjectTreeNodeDataResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadMPProjectTreeNodeData"
			body: "*"
		};
	}

	rpc DownLoadLibraryTreeNode(DownLoadLibraryTreeNodeRequest) returns (DownLoadLibraryTreeNodeResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadLibraryTreeNode"
			body: "*"
		};
	}

	rpc DownLoadLibFileData(DownLoadLibFileDataRequest) returns (DownLoadLibFileDataResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadLibFileData"
			body: "*"
		};
	}

	rpc GetLibraryFileMD5(GetLibraryFileMD5Request) returns (GetLibraryFileMD5Response){
		option (google.api.http) = {
			post: "/v1/GetLibraryFileMD5"
			body: "*"
		};
	}
	rpc GetMPTeamProjectInitState(GetMPTeamProjectInitStateRequest) returns (GetMPTeamProjectInitStateResponse){
		option (google.api.http) = {
			post: "/v1/GetMPTeamProjectInitState"
			body: "*"
		};
	}
	rpc DownLoadLibraryTreeNodeChange(DownLoadLibraryTreeNodeChangeRequest) returns (DownLoadLibraryTreeNodeChangeResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadLibraryTreeNodeChange"
			body: "*"
		};
	}

	rpc DownLoadLibraryTreeNodeLarge(DownLoadLibraryTreeNodeLargeRequest) returns (DownLoadLibraryTreeNodeLargeResponse){
		option (google.api.http) = {
			post: "/v1/DownLoadLibraryTreeNodeLarge"
			body: "*"
		};
	}
	
	rpc GetMPProjectTreeNodesByNodeName(GetMPProjectTreeNodesByNodeNameRequest) returns (GetMPProjectTreeNodesByNodeNameResponse){
		option (google.api.http) = {
			post: "/v1/GetMPProjectTreeNodesByNodeName"
			body: "*"
		};
	}
	
	rpc GetParentsMPProjectTreeNodesByNodeId(GetParentsMPProjectTreeNodesByNodeIdRequest) returns (GetParentsMPProjectTreeNodesByNodeIdResponse){
		option (google.api.http) = {
			post: "/v1/GetParentsMPProjectTreeNodesByNodeId"
			body: "*"
		};
	}
	
	rpc GetChildsMPProjectTreeNodesByNodeId(GetChildsMPProjectTreeNodesByNodeIdRequest) returns (GetChildsMPProjectTreeNodesByNodeIdResponse){
		option (google.api.http) = {
			post: "/v1/GetChildsMPProjectTreeNodesByNodeId"
			body: "*"
		};
	}

	rpc GetChildsMPProjectTreeCountByNodeId(GetChildsMPProjectTreeCountByNodeIdRequest) returns (GetChildsMPProjectTreeCountByNodeIdResponse){
		option (google.api.http) = {
			post: "/v1/mainprojectquery/GetChildsMPProjectTreeCountByNodeId"
			body: "*"
		};
	}

	// 新增：服务端流式方法
	rpc GetChildsMPProjectTreeNodesByNodeIdStream(GetChildsMPProjectTreeNodesByNodeIdStreamRequest) returns (stream GetChildsMPProjectTreeNodesByNodeIdStreamResponse){
		option (google.api.http) = {
			post: "/v1/mainprojectquery/GetChildsMPProjectTreeNodesByNodeIdStream"
			body: "*"
		};
	}
}