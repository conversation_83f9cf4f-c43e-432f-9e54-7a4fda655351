================================================================================
gRPC调用结果 - 2025-06-18 12:36:59
配置文件: DownLoadMPProjectTreeNodeData/request.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcMainprojectQuery/DownLoadMPProjectTreeNodeData
   📝 数据: {"mainprojectGuid": "82b3a9d9-9ba5-456f-9494-24054b5a9774", "sessionId": "ce148fea9c5349e8b09ab0f9781ea6cf", "subProjectGuid": "195259ef-38b6-43de-bf10-89480d062a44", "treeId": "17656610553961"}
   🏷️  Metadata:
      client-id: PKPM-Plant
      session-id: ce148fea9c5349e8b09ab0f9781ea6cf
      client-version: BIMBase-2025R01.01

📥 响应信息:

❌ 错误: Command '['grpcurl', '-plaintext', '-v', '-import-path', 'F:\\lvmeng\\work_code\\BIMBaseServer\\BIMBaseServer\\src\\Proto', '-proto', 'mainprojectquery.proto', '-H', 'client-id: PKPM-Plant', '-H', 'session-id: ce148fea9c5349e8b09ab0f9781ea6cf', '-H', 'client-version: BIMBase-2025R01.01', '-d', '{"mainprojectGuid": "82b3a9d9-9ba5-456f-9494-24054b5a9774", "sessionId": "ce148fea9c5349e8b09ab0f9781ea6cf", "subProjectGuid": "195259ef-38b6-43de-bf10-89480d062a44", "treeId": "17656610553961"}', '192.168.190.183:5002', 'bimbase.api.GrpcMainprojectQuery/DownLoadMPProjectTreeNodeData']' timed out after 30 seconds

================================================================================
