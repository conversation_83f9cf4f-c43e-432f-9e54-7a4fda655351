================================================================================
gRPC调用结果 - 2025-06-18 12:25:08
配置文件: login/login_with_metadata.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "lvmeng", "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced

📥 响应信息:
   📊 响应Body:
      Resolved method descriptor:
      rpc Login ( .bimbase.api.LoginRequest ) returns ( .bimbase.api.LoginResponse ) {
        option (.google.api.http) = {
          get: "/v1/Login/username/{userName}/passwordMd5/{passwordMd5}"
        };
      }
      
      Request metadata to send:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced
      
      Response headers received:
      cache-control: no-cache,no-store
      content-type: application/grpc
      date: Wed, 18 Jun 2025 04:25:08 GMT
      expires: -1
      pragma: no-cache
      server: Kestrel
      set-cookie: .AspNetCore.Session=CfDJ8JmiJM0C43ZFuEQB2tyVbE9AI7a5ljWjDFvTFetm%2BGqfJkzXT4i6GAMH6rOboG7kMHqg3rHeBNSjQJaa5cmskU9%2Bz6dkuOUb0vKcDv0C%2BVkkgIiFil3yL0nQn7fv4LT9P2EZ42zEsV3vdFSdCA2oUORMNY6kasgwmyjwb8E321uc; path=/; samesite=lax; httponly
      
      Response contents:
      {
        "isSuccess": true,
        "message": "登录成功。",
        "sessionId": "0979c5541c27473b85bc45c1b3c0c332"
      }
      
      Response trailers received:
      processing-time-ms: 14
      request-id: 0HNDDJIGOV2JL:00000001
      Sent 1 request and received 1 response

🔧 调用详情:
   返回码: 0

================================================================================
