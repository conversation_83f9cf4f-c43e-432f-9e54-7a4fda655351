================================================================================
gRPC调用结果 - 2025-06-18 13:30:17
配置文件: login\request.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "lvmeng", "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced

📥 响应信息:
   📊 响应Body:
      Resolved method descriptor:
      rpc Login ( .bimbase.api.LoginRequest ) returns ( .bimbase.api.LoginResponse ) {
        option (.google.api.http) = {
          get: "/v1/Login/username/{userName}/passwordMd5/{passwordMd5}"
        };
      }
      
      Request metadata to send:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced
      
      Response headers received:
      cache-control: no-cache,no-store
      content-type: application/grpc
      date: Wed, 18 Jun 2025 05:30:17 GMT
      expires: -1
      pragma: no-cache
      server: Kestrel
      set-cookie: .AspNetCore.Session=CfDJ8JmiJM0C43ZFuEQB2tyVbE%2B39JH2pVNApMDprshrUK2gLNU6S59SDRXTUuVapUnOO%2BGOKuRBctQdvIYUI2wt%2Bm1E5AQr6Fv5FhE73jE3FeUDNKyjbbwjya3sXIjU6vDXAXeEO7uAbvsv%2BI2%2BFD0v3RriQOG3xT%2BstbvjqOeiJAgD; path=/; samesite=lax; httponly
      
      Response contents:
      {
        "isSuccess": true,
        "message": "登录成功。",
        "sessionId": "90f06b3ece144d0da8b6bc5d81b58713"
      }
      
      Response trailers received:
      processing-time-ms: 12
      request-id: 0HNDDJIGOV2K3:00000001
      Sent 1 request and received 1 response

🔧 调用详情:
   返回码: 0

================================================================================
