================================================================================
gRPC调用结果 - 2025-06-18 12:37:35
配置文件: login/login_with_metadata.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "lvmeng", "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced

📥 响应信息:
   📊 响应Body:
      Resolved method descriptor:
      rpc Login ( .bimbase.api.LoginRequest ) returns ( .bimbase.api.LoginResponse ) {
        option (.google.api.http) = {
          get: "/v1/Login/username/{userName}/passwordMd5/{passwordMd5}"
        };
      }
      
      Request metadata to send:
      client-version: 1.0.0
      request-id: req-12345
      user-agent: grpc-client-advanced
      
      Response headers received:
      cache-control: no-cache,no-store
      content-type: application/grpc
      date: Wed, 18 Jun 2025 04:37:35 GMT
      expires: -1
      pragma: no-cache
      server: Kestrel
      set-cookie: .AspNetCore.Session=CfDJ8JmiJM0C43ZFuEQB2tyVbE9HYZfVOl3O4%2BfaWUxky2cFnXqcG6b0dBj7x%2FAqlbXBtCekSLBUm%2BF7g9oXf7fd8HGuzP2MK2BYF0O54hSNMEhMB8ssxkY52PaE8rUjOZSX1DSqtY9Vh91zrNG2JXgSlMpT9gO7Q1X4QyIM9AOqj%2Bju; path=/; samesite=lax; httponly
      
      Response contents:
      {
        "isSuccess": true,
        "message": "登录成功。",
        "sessionId": "********************************"
      }
      
      Response trailers received:
      processing-time-ms: 14
      request-id: 0HNDDJIGOV2JQ:00000001
      Sent 1 request and received 1 response

🔧 调用详情:
   返回码: 0

================================================================================
