proto : connection.proto
method : bimbase.api.GrpcConnection/Login

-metadata :
client-version: 1.0.0
request-id: req-67890
user-agent: grpc-client-test

-d : 
{
    "userName": "testuser",
    "passwordMd5": "5D41402ABC4B2A76B9719D911017C592"
}

# ===== 用户数据暂存区域 =====
# 以下数据仅用于暂存，不会被读取使用

-users :
# 用户1: lvmeng (主账号)
userName: lvmeng
passwordMd5: D4D5CC8034000B7A0EE386FD99C5F3E0
note: 主要测试账号

# 用户2: testuser (当前使用)
userName: testuser
passwordMd5: 5D41402ABC4B2A76B9719D911017C592
note: 测试账号，密码是hello

# 用户3: admin
userName: admin
passwordMd5: 21232F297A57A5A743894A0E4A801FC3
note: 管理员账号，密码是admin

# 用户4: guest
userName: guest
passwordMd5: 084E0343A0486FF05530DF6C705C8BB4
note: 访客账号，密码是guest

# 项目相关信息：
# - 项目: BIMBase系统测试
# - 环境: 开发环境
# - 服务器: 192.168.190.183:5002
# - 文档: 保存在暂存区域，方便随时查看和切换用户 