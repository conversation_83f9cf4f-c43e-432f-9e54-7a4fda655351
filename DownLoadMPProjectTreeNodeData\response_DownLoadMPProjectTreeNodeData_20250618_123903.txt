================================================================================
gRPC调用结果 - 2025-06-18 12:39:03
配置文件: DownLoadMPProjectTreeNodeData/request.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcMainprojectQuery/DownLoadMPProjectTreeNodeData
   📝 数据: {"mainprojectGuid": "82b3a9d9-9ba5-456f-9494-24054b5a9774", "sessionId": "15ee4efe658e4e429b193846efe92da7", "subProjectGuid": "195259ef-38b6-43de-bf10-89480d062a44", "treeId": "17656610553961"}
   🏷️  Metadata:
      client-id: PKPM-Plant
      session-id: 15ee4efe658e4e429b193846efe92da7
      client-version: BIMBase-2025R01.01

📥 响应信息:

❌ 错误: ERROR:
  Code: ResourceExhausted
  Message: grpc: received message larger than max (58381346 vs. 4194304)

🔧 调用详情:
   返回码: 72
   详细信息:
      ERROR:
        Code: ResourceExhausted
        Message: grpc: received message larger than max (58381346 vs. 4194304)

================================================================================
