================================================================================
gRPC调用结果 - 2025-06-18 12:22:15
配置文件: login/login_testuser.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "testuser", "passwordMd5": "5D41402ABC4B2A76B9719D911017C592"}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-67890
      user-agent: grpc-client-test

📥 响应信息:
   📊 响应Body:
      Resolved method descriptor:
      rpc Login ( .bimbase.api.LoginRequest ) returns ( .bimbase.api.LoginResponse ) {
        option (.google.api.http) = {
          get: "/v1/Login/username/{userName}/passwordMd5/{passwordMd5}"
        };
      }
      
      Request metadata to send:
      client-version: 1.0.0
      request-id: req-67890
      user-agent: grpc-client-test
      
      Response headers received:
      content-type: application/grpc
      date: Wed, 18 Jun 2025 04:22:15 GMT
      server: Kestrel
      
      Response contents:
      {
        "message": "用户名密码输入错误，请检查您的输入。"
      }
      
      Response trailers received:
      processing-time-ms: 3
      request-id: 0HNDDJIGOV2JK:00000001
      Sent 1 request and received 1 response

🔧 调用详情:
   返回码: 0

================================================================================
