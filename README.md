# gRPC完整调用工具

这是一个高级的gRPC调用工具集，支持完整的metadata、headers、trailers信息获取。

## 🎯 核心工具

### `grpc.py` - 完整gRPC信息工具
获取完整的gRPC响应信息，包括headers、body、trailers。

**使用方法：**
```bash
python grpc.py <host> <port> <config_file>
```

**示例：**
```bash
python grpc.py *************** 5002 login/login_with_metadata.txt
python grpc.py *************** 5002 login/login_testuser.txt
```

## 📁 目录结构

```
test_grpcurl/
├── grpc.py                           # 主程序（简洁命名）
├── README.md                         # 说明文档
├── userinput.py                      # 交互循环工具
└── login/                            # 登录服务目录
    ├── login_with_metadata.txt       # 主用户配置
    ├── login_testuser.txt           # 测试用户配置
    ├── response_Login_20250618_122159.txt  # 响应文件（时间戳）
    └── response_Login_20250618_122215.txt  # 响应文件（时间戳）
```

**目录管理优点：**
- 🗂️ **服务分类**：不同gRPC服务有独立目录
- 📄 **配置集中**：相关配置文件放在同一目录
- 📊 **响应归档**：响应文件与配置文件在同目录
- 🕒 **时间戳命名**：每次调用生成独立的响应文件

## 📄 配置文件格式

### `login/login_with_metadata.txt` - 带metadata的登录配置
```
proto : connection.proto
method : bimbase.api.GrpcConnection/Login

-metadata :
client-version: 1.0.0
request-id: req-12345
user-agent: grpc-client-advanced

-headers :
content-type: application/grpc+json
accept: application/json

-d : 
{
    "userName": "lvmeng",
    "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"
}

# ===== 用户数据暂存区域 =====
# 以下数据仅用于暂存，不会被读取使用

-users :
# 用户1: lvmeng (当前使用)
userName: lvmeng
passwordMd5: D4D5CC8034000B7A0EE386FD99C5F3E0
note: 主要测试账号

# 用户2: testuser
userName: testuser
passwordMd5: 5D41402ABC4B2A76B9719D911017C592
note: 测试账号，密码是hello

# 用户3: 管理员
userName: admin
passwordMd5: 21232F297A57A5A743894A0E4A801FC3
note: 管理员账号，密码是admin
```

## 🔍 功能特性

- ✅ **完整信息获取**：获取请求和响应的完整metadata、headers、trailers
- ✅ **配置文件支持**：避免复杂的命令行JSON输入
- ✅ **用户数据暂存**：在配置文件中暂存多个用户信息，请求时自动忽略
- ✅ **目录结构管理**：支持按服务分类管理配置和响应文件
- ✅ **时间戳文件名**：每次调用生成独立的响应文件，便于历史追踪
- ✅ **多proto支持**：支持不同的.proto文件
- ✅ **格式化输出**：JSON响应自动格式化显示
- ✅ **错误处理**：详细的错误信息和调试输出

## 📊 输出示例

### 终端输出（简要摘要）
```
✅ 结果已保存到文件: login\response_Login_20250618_122159.txt
📊 调用摘要:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📋 响应: {"isSuccess": true, "message": "登录成功。", "sessionId": "xxx"}
   📁 输出目录: login
   📄 详细信息: response_Login_20250618_122159.txt
```

### 文件输出（完整信息）
```
================================================================================
gRPC调用结果 - 2025-06-18 12:21:59
配置文件: login/login_with_metadata.txt
================================================================================

📤 请求信息:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📝 数据: {"userName": "lvmeng", "passwordMd5": "..."}
   🏷️  Metadata:
      client-version: 1.0.0
      request-id: req-12345

📥 响应信息:
   📋 响应Headers:
      cache-control: no-cache,no-store
      content-type: application/grpc
      server: Kestrel
   
   📊 响应Body:
      {
        "isSuccess": true,
        "message": "登录成功。",
        "sessionId": "f755bcdd3cb54bcea4dd7a8bc13a928c"
      }
   
   🔚 响应Trailers:
      processing-time-ms: 13
      request-id: 0HNDDJIGOV2JH:00000001
```

## 🛠️ 环境要求

- Python 3.6+
- grpcurl 命令行工具
- proto文件路径：`F:\lvmeng\work_code\BIMBaseServer\BIMBaseServer\src\Proto`

## 📝 配置文件说明

### 基本结构
```
proto : <proto_file>         # 指定.proto文件
method : <service/method>     # 指定服务方法

-metadata :                   # gRPC metadata（可选）
key1: value1
key2: value2

-headers :                    # HTTP headers（可选）  
header1: value1
header2: value2

-d :                          # JSON请求数据
{
    "field1": "value1",
    "field2": "value2"
}

# ===== 用户数据暂存区域 =====
# 以下数据仅用于暂存，不会被读取使用
```

### 支持的proto文件
- `connection.proto` - 连接和登录相关
- `teamquery.proto` - 团队查询相关
- 其他自定义proto文件

## 🚀 快速开始

1. 确保grpcurl已安装并配置在PATH中
2. 根据需要修改配置文件中的参数
3. 运行工具：
   ```bash
   python grpc.py *************** 5002 login/login_with_metadata.txt
   ```

## 📈 扩展其他服务

创建新服务目录，例如 `team/`、`project/` 等：

```bash
# 创建团队服务目录
mkdir team
# 添加团队查询配置
# team/team_query.txt

# 创建项目服务目录  
mkdir project
# 添加项目配置
# project/project_list.txt
```

每个服务目录都会：
- 📁 独立管理配置文件
- 📊 响应文件自动保存到同目录
- 🕒 使用时间戳避免文件冲突
- 🗂️ 便于服务分类和管理

## 🎯 新增功能

### 📚 用户数据暂存功能
在配置文件末尾可以添加暂存区域，用于存储多个用户信息：

```
# ===== 用户数据暂存区域 =====
# 以下数据仅用于暂存，不会被读取使用

-users :
# 用户1: lvmeng (当前使用)
userName: lvmeng
passwordMd5: D4D5CC8034000B7A0EE386FD99C5F3E0
note: 主要测试账号

# 用户2: testuser
userName: testuser
passwordMd5: 5D41402ABC4B2A76B9719D911017C592
note: 测试账号，密码是hello
```

**优点：**
- 🔒 **自动忽略**：程序会自动检测并忽略暂存区域
- 📝 **便于管理**：多个用户信息保存在同一文件中
- 🔄 **快速切换**：复制粘贴即可切换不同用户
- 📋 **备注功能**：可以添加用户说明和密码提示

### 📄 结果文件输出功能
- ✅ 结果自动保存到配置文件同目录
- ✅ 使用时间戳生成独立文件名：`response_ServiceName_YYYYMMDD_HHMMSS.txt`
- ✅ 终端只显示简要摘要信息
- ✅ 每次调用生成新文件，用户可自行管理和删除

**示例输出：**
```
✅ 结果已保存到文件: login\response_Login_20250618_122159.txt
📊 调用摘要:
   🎯 方法: bimbase.api.GrpcConnection/Login
   📋 响应: {"isSuccess": true, "message": "登录成功。"}
   📁 输出目录: login
   📄 详细信息: response_Login_20250618_122159.txt
```

---

这个工具完美解决了gRPC调用中复杂JSON输入和完整响应信息获取的问题！ 