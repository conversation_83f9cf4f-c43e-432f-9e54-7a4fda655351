proto : connection.proto
method : bimbase.api.GrpcConnection/Login

-metadata :
client-version: 1.0.0
request-id: req-12345
user-agent: grpc-client-advanced

-headers :
content-type: application/grpc+json
accept: application/json

-d : 
{
    "userName": "lvmeng",
    "passwordMd5": "D4D5CC8034000B7A0EE386FD99C5F3E0"
}

# ===== 用户数据暂存区域 =====
# 以下数据仅用于暂存，不会被读取使用

-users :
# 用户1: lvmeng (当前使用)
userName: lvmeng
passwordMd5: D4D5CC8034000B7A0EE386FD99C5F3E0
note: 主要测试账号

# 用户2: 测试用户
userName: testuser
passwordMd5: 5D41402ABC4B2A76B9719D911017C592
note: 测试账号，密码是hello

# 用户3: 管理员
userName: admin
passwordMd5: 21232F297A57A5A743894A0E4A801FC3
note: 管理员账号，密码是admin

# 其他备注信息：
# - 服务器地址: 192.168.190.183:5002
# - 测试时间: 2025-06-18
# - 开发环境配置 