#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
gRPC完整信息工具 - 增强版
获取完整的gRPC响应信息，包括headers、body、trailers
支持用户数据暂存和响应输出到文件
"""

import subprocess
import json
import os
import sys
import re
import datetime
from typing import Dict, List, Optional

class CompleteGrpcCaller:
    """完整gRPC调用器"""
    
    def __init__(self, host: str, port: int, proto_path: str):
        """初始化调用器
        
        Args:
            host (str): gRPC服务器主机
            port (int): gRPC服务器端口
            proto_path (str): proto文件路径
        """
        self._host = host
        self._port = port
        self._address = f"{host}:{port}"
        self._proto_path = proto_path
    
    def call_with_verbose_output(self, proto_file: str, service_method: str, 
                                json_data: dict, metadata: dict = None) -> Dict[str, any]:
        """使用详细输出调用gRPC服务
        
        Args:
            proto_file (str): proto文件名
            service_method (str): 服务方法
            json_data (dict): JSON数据
            metadata (dict): metadata
            
        Returns:
            Dict: 完整调用结果
        """
        result = {
            "request": {
                "service_method": service_method,
                "data": json_data,
                "metadata": metadata or {}
            },
            "response": {
                "headers": None,
                "body": None,
                "trailers": None
            },
            "raw_output": None,
            "error": None
        }
        
        try:
            # 构建grpcurl命令，使用-v获取详细信息
            cmd = [
                "grpcurl",
                "-plaintext",
                "-v",  # verbose输出，包含headers和trailers
                "-max-msg-sz", "2147483648",  # 设置最大消息大小为2GB
                "-import-path", self._proto_path,
                "-proto", proto_file
            ]
            
            # 添加metadata
            if metadata:
                for key, value in metadata.items():
                    cmd.extend(["-H", f"{key}: {value}"])
            
            # 添加JSON数据
            json_str = json.dumps(json_data, ensure_ascii=False)
            cmd.extend(["-d", json_str])
            
            # 添加服务地址和方法
            cmd.extend([self._address, service_method])
            
            # 执行命令
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=120,  # 增加到120秒
                encoding='utf-8',
                errors='ignore'
            )
            
            # 保存原始输出
            result["raw_output"] = {
                "stdout": process.stdout,
                "stderr": process.stderr,
                "returncode": process.returncode
            }
            
            if process.returncode == 0:
                # 解析详细输出
                self._parse_verbose_output(process.stderr, process.stdout, result)
            else:
                result["error"] = process.stderr.strip() if process.stderr else "调用失败"
            
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_verbose_output(self, stderr: str, stdout: str, result: dict):
        """解析详细输出
        
        Args:
            stderr (str): 错误输出（grpcurl的verbose信息通常在stderr）
            stdout (str): 标准输出（响应body）
            result (dict): 结果字典
        """
        # 解析响应body
        if stdout.strip():
            try:
                response_json = json.loads(stdout.strip())
                result["response"]["body"] = response_json
                result["response"]["body_formatted"] = json.dumps(response_json, indent=2, ensure_ascii=False)
            except json.JSONDecodeError:
                result["response"]["body"] = stdout.strip()
                result["response"]["body_formatted"] = stdout.strip()
        
        # 解析headers和trailers（从stderr中）
        if stderr:
            lines = stderr.split('\n')
            current_section = None
            headers = {}
            trailers = {}
            
            for line in lines:
                line = line.strip()
                
                # 识别不同的部分
                if "Request Headers:" in line or "Outgoing Headers:" in line:
                    current_section = "request_headers"
                elif "Response Headers:" in line or "Incoming Headers:" in line:
                    current_section = "response_headers"
                elif "Response Trailers:" in line or "Incoming Trailers:" in line:
                    current_section = "response_trailers"
                elif line.startswith('*') or line.startswith('Connected to') or line.startswith('Response contents:'):
                    current_section = None
                
                # 解析header/trailer行
                if current_section and ':' in line and not line.startswith('*'):
                    try:
                        key, value = line.split(':', 1)
                        key = key.strip()
                        value = value.strip()
                        
                        if current_section == "response_headers":
                            headers[key] = value
                        elif current_section == "response_trailers":
                            trailers[key] = value
                    except:
                        continue
            
            if headers:
                result["response"]["headers"] = headers
            if trailers:
                result["response"]["trailers"] = trailers

def parse_config_file(config_file: str) -> Dict[str, any]:
    """解析配置文件（增强版，支持用户暂存区域忽略）
    
    Args:
        config_file (str): 配置文件路径
        
    Returns:
        Dict: 配置信息
    """
    config = {
        "proto_file": None,
        "method": None,
        "json_data": {},
        "metadata": {},
        "error": None
    }
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找用户暂存区域标记，如果找到就截取到该位置
        storage_marker = "# ===== 用户数据暂存区域 ====="
        if storage_marker in content:
            content = content[:content.find(storage_marker)]
            print("💡 检测到用户暂存区域，已自动忽略暂存数据")
        
        # 解析proto文件
        proto_match = re.search(r'proto\s*:\s*(.+)', content)
        if proto_match:
            config["proto_file"] = proto_match.group(1).strip()
        
        # 解析方法
        method_match = re.search(r'method\s*:\s*(.+)', content)
        if method_match:
            config["method"] = method_match.group(1).strip()
        
        # 解析metadata
        metadata_start = content.find('-metadata :')
        if metadata_start != -1:
            metadata_section = content[metadata_start + 11:]
            data_start = metadata_section.find('-d :')
            headers_start = metadata_section.find('-headers :')
            
            end_pos = len(metadata_section)
            if data_start != -1:
                end_pos = min(end_pos, data_start)
            if headers_start != -1:
                end_pos = min(end_pos, headers_start)
            
            metadata_content = metadata_section[:end_pos].strip()
            
            for line in metadata_content.split('\n'):
                line = line.strip()
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    config["metadata"][key.strip()] = value.strip()
        
        # 解析JSON数据
        json_start = content.find('-d :')
        if json_start != -1:
            json_part = content[json_start + 4:].strip()
            # 去除可能的末尾注释
            if json_part.endswith('}'):
                brace_pos = json_part.rfind('}')
                json_part = json_part[:brace_pos + 1]
            
            try:
                config["json_data"] = json.loads(json_part)
            except json.JSONDecodeError as e:
                config["error"] = f"JSON格式错误: {e}"
    
    except Exception as e:
        config["error"] = str(e)
    
    return config

def format_result_for_output(result: Dict[str, any], config_file: str) -> str:
    """格式化结果为输出字符串
    
    Args:
        result (Dict): 调用结果
        config_file (str): 配置文件名
        
    Returns:
        str: 格式化后的输出字符串
    """
    output_lines = []
    
    # 添加时间戳和分隔符
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    output_lines.append("=" * 80)
    output_lines.append(f"gRPC调用结果 - {timestamp}")
    output_lines.append(f"配置文件: {config_file}")
    output_lines.append("=" * 80)
    
    # 显示请求信息
    output_lines.append("\n📤 请求信息:")
    output_lines.append(f"   🎯 方法: {result['request']['service_method']}")
    output_lines.append(f"   📝 数据: {json.dumps(result['request']['data'], ensure_ascii=False)}")
    
    if result['request']['metadata']:
        output_lines.append("   🏷️  Metadata:")
        for key, value in result['request']['metadata'].items():
            output_lines.append(f"      {key}: {value}")
    
    # 显示响应信息
    output_lines.append("\n📥 响应信息:")
    
    if result['response']['headers']:
        output_lines.append("   📋 响应Headers:")
        for key, value in result['response']['headers'].items():
            output_lines.append(f"      {key}: {value}")
    
    if result['response']['body']:
        output_lines.append("   📊 响应Body:")
        if result['response'].get('body_formatted'):
            for line in result['response']['body_formatted'].split('\n'):
                output_lines.append(f"      {line}")
        else:
            output_lines.append(f"      {result['response']['body']}")
    
    if result['response']['trailers']:
        output_lines.append("   🔚 响应Trailers:")
        for key, value in result['response']['trailers'].items():
            output_lines.append(f"      {key}: {value}")
    
    if result['error']:
        output_lines.append(f"\n❌ 错误: {result['error']}")
    
    # 添加原始输出信息（调试用）
    if result.get('raw_output'):
        output_lines.append(f"\n🔧 调用详情:")
        output_lines.append(f"   返回码: {result['raw_output']['returncode']}")
        if result['raw_output']['stderr']:
            output_lines.append("   详细信息:")
            for line in result['raw_output']['stderr'][:1000].split('\n'):  # 限制长度
                if line.strip():
                    output_lines.append(f"      {line}")
    
    output_lines.append("\n" + "=" * 80 + "\n")
    
    return "\n".join(output_lines)

def save_result_to_file(result: Dict[str, any], config_file: str, output_file: str = None):
    """保存结果到文件（支持目录结构和时间戳）
    
    Args:
        result (Dict): 调用结果
        config_file (str): 配置文件路径
        output_file (str): 输出文件名（可选，默认使用时间戳）
    """
    try:
        # 确定输出目录（与配置文件同目录）
        config_dir = os.path.dirname(config_file) if os.path.dirname(config_file) else "."
        
        # 生成时间戳文件名
        if output_file is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            service_name = result['request']['service_method'].split('/')[-1] if '/' in result['request']['service_method'] else "response"
            output_file = f"response_{service_name}_{timestamp}.txt"
        
        # 完整输出路径
        output_path = os.path.join(config_dir, output_file)
        
        # 生成输出内容
        output_content = format_result_for_output(result, config_file)
        
        # 写入文件（每次都是新文件，不是追加）
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(output_content)
        
        print(f"✅ 结果已保存到文件: {output_path}")
        
        # 显示简要信息到终端
        print("📊 调用摘要:")
        print(f"   🎯 方法: {result['request']['service_method']}")
        if result['response']['body']:
            if isinstance(result['response']['body'], dict):
                # 提取关键响应信息
                body = result['response']['body']
                if isinstance(body, dict):
                    # 尝试提取常见的状态信息
                    summary_info = {}
                    for key in ['isSuccess', 'success', 'status', 'message', 'msg', 'sessionId', 'token']:
                        if key in body:
                            summary_info[key] = body[key]
                    if summary_info:
                        print(f"   📋 响应: {json.dumps(summary_info, ensure_ascii=False)}")
                    else:
                        # 如果没有标准字段，显示前100个字符
                        response_str = json.dumps(body, ensure_ascii=False)
                        print(f"   📋 响应: {response_str[:100]}{'...' if len(response_str) > 100 else ''}")
                else:
                    print(f"   📋 响应: {json.dumps(result['response']['body'], ensure_ascii=False)}")
            else:
                print(f"   📋 响应: {str(result['response']['body'])[:100]}...")
        if result['error']:
            print(f"   ❌ 错误: {result['error']}")
        print(f"   📁 输出目录: {config_dir}")
        print(f"   📄 详细信息: {output_file}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")
        # 如果保存失败，回退到终端显示
        display_complete_result(result)

def display_complete_result(result: Dict[str, any]):
    """显示完整结果到终端（备用方案）
    
    Args:
        result (Dict): 调用结果
    """
    print("🔍 完整gRPC调用信息")
    print("=" * 60)
    
    # 显示请求信息
    print("📤 请求信息:")
    print(f"   🎯 方法: {result['request']['service_method']}")
    print(f"   📝 数据: {json.dumps(result['request']['data'], ensure_ascii=False)}")
    
    if result['request']['metadata']:
        print(f"   🏷️  Metadata:")
        for key, value in result['request']['metadata'].items():
            print(f"      {key}: {value}")
    
    # 显示响应信息
    print("\n📥 响应信息:")
    
    if result['response']['headers']:
        print("   📋 响应Headers:")
        for key, value in result['response']['headers'].items():
            print(f"      {key}: {value}")
    
    if result['response']['body']:
        print("   📊 响应Body:")
        if result['response'].get('body_formatted'):
            print(f"      {result['response']['body_formatted']}")
        else:
            print(f"      {result['response']['body']}")
    
    if result['response']['trailers']:
        print("   🔚 响应Trailers:")
        for key, value in result['response']['trailers'].items():
            print(f"      {key}: {value}")
    
    if result['error']:
        print(f"\n❌ 错误: {result['error']}")

def main():
    """主函数"""
    if len(sys.argv) != 4:
        print("使用方法:")
        print("  python grpc.py <host> <port> <config_file>")
        print("示例:")
        print("  python grpc.py *************** 5002 login/login_with_metadata.txt")
        print("  python grpc.py *************** 5002 login/login_testuser.txt")
        print("\n功能特性:")
        print("  ✅ 自动忽略配置文件中的用户暂存区域")
        print("  ✅ 结果保存到配置文件同目录，使用时间戳文件名")
        print("  ✅ 终端显示简要摘要信息")
        print("  ✅ 支持目录结构管理不同服务的配置")
        return 1
    
    try:
        host = sys.argv[1]
        port = int(sys.argv[2])
        config_file = sys.argv[3]
        
        print(f"🚀 启动gRPC完整信息调用...")
        print(f"📄 配置文件: {config_file}")
        
        # 解析配置文件
        config = parse_config_file(config_file)
        if config["error"]:
            print(f"❌ 配置错误: {config['error']}")
            return 1
        
        if not config["proto_file"] or not config["method"]:
            print("❌ 配置文件必须包含proto文件和方法")
            return 1
        
        # 创建调用器
        proto_path = r"F:\lvmeng\work_code\BIMBaseServer\BIMBaseServer\src\Proto"
        caller = CompleteGrpcCaller(host, port, proto_path)
        
        print(f"📞 调用中...")
        
        # 执行调用
        result = caller.call_with_verbose_output(
            config["proto_file"],
            config["method"],
            config["json_data"],
            config["metadata"]
        )
        
        # 保存结果到文件
        save_result_to_file(result, config_file)
        
        return 0
        
    except ValueError:
        print("错误: 端口必须是数字")
        return 1
    except Exception as e:
        print(f"程序运行出错: {e}")
        return 1

if __name__ == "__main__":
    exit(main()) 