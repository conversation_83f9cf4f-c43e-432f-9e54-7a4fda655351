syntax = "proto3";

package sample;

// 示例服务定义
service SampleService {
    // 问候方法
    rpc Say<PERSON>ello(HelloRequest) returns (HelloResponse);
    
    // 获取状态方法
    rpc GetStatus(StatusRequest) returns (StatusResponse);
}

// 请求消息
message HelloRequest {
    string name = 1;
}

// 响应消息
message HelloResponse {
    string message = 1;
}

// 状态请求
message StatusRequest {
    // 空请求
}

// 状态响应
message StatusResponse {
    string status = 1;
    int32 code = 2;
}
